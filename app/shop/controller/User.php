<?php

namespace app\shop\controller;

use app\common\controller\Base;
use think\response\Json;
use app\shop\validate\EditStaffInfo as EditStaffInfoValidate;
use app\shop\validate\EditStaffAuthn as EditStaffAuthnValidate;
use app\shop\validate\EditStaffRest as EditStaffRestValidate;
use app\shop\validate\EditStaffArea as EditStaffAreaValidate;
use app\shop\validate\EditStaffTrain as EditStaffTrainValidate;
use app\shop\validate\StaffCalendar as StaffCalendarValidate;
use app\shop\validate\Feedback as FeedbackValidate;
use app\common\service\StaffService;
use app\common\enum\Response;
use app\common\service\FeedbackService;

class User extends Base
{
    public function getInfo(): Json
    {
        $service = new StaffService();
        return json($service->getStaff(intval($this->param['staff_id'])));
    }

    public function getResume(): Json
    {
        $id = intval($this->param['staff_id']);
        if ($id <= 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "服务人员ID错误"));
        }
        $service = new StaffService();
        $info = $service->getStaff(intval($id));
        if ($info['data']['audit_status'] == 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "该服务人员正在审核中"));
        }
        return json($info);
    }

    public function editInfo(): Json
    {
        $validate = new EditStaffInfoValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        // 修改自身
        $this->param['id'] = $this->param['staff_id'];
        $service = new StaffService();
        return json($service->edit($this->param));
    }

    public function addAuthn(): Json
    {
        $validate = new EditStaffAuthnValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->addAuthImage($this->param));
    }

    public function editAuthn(): Json
    {
        $validate = new EditStaffAuthnValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->editAuthImage($this->param));
    }

    public function deleteAuthn(): Json
    {
        $service = new StaffService();
        return json($service->delAuthImage($this->param));
    }

    public function getRestTime(): Json
    {
        $service = new StaffService();
        $res = $service->getRestTime(intval($this->param['staff_id']));
        if ($res !== false) {
            return json(success($res));
        }
        return json(fail());
    }

    // 休息时间
    public function setRestTime(): Json
    {
        $validate = new EditStaffRestValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->setRestTime($this->param));
    }

    public function deleteRestTime(): Json
    {
        $service = new StaffService();
        return json($service->delRestTime($this->param));
    }

    public function getArea(): Json
    {
        $service = new StaffService();
        $res = $service->getArea(intval($this->param['staff_id']));
        if ($res !== false) {
            return json(success($res));
        }
        return json(fail());
    }

    // 接单区域
    public function setArea(): Json
    {
        $validate = new EditStaffAreaValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->setArea($this->param));
    }

    public function addTrain(): Json
    {
        $validate = new EditStaffTrainValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->addTrain($this->param));
    }

    public function editTrain(): Json
    {
        $validate = new EditStaffTrainValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->editTrain($this->param));
    }

    public function deleteTrain(): Json
    {
        $service = new StaffService();
        return json($service->delTrain($this->param));
    }

    public function addFeedback(): Json
    {
        $validate = new FeedbackValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new FeedbackService();
        return json($service->add($this->param));
    }

    public function getShopInfo(): Json
    {
        $service = new StaffService();
        $res = $service->getStaffShopById(intval($this->param['staff_id']));
        if ($res !== false) {
            return json(success($res));
        }
        return json(fail(Response::REQUEST_PARAM_ERROR, "找不到对应信息"));
    }

    // public function setService(): Json
    // {
    //     $service = new StaffService();
    //     return json($service->setService($this->param));
    // }

    public function getCalendar(): Json
    {
        $validate = new StaffCalendarValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        $service = new StaffService();
        $list = $service->getWorkList($this->param);
        return json(success(array_values($list)));
    }
}
