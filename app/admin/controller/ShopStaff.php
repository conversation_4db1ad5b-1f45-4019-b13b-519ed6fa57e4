<?php

namespace app\admin\controller;

use app\common\controller\Base;
use think\response\Json;
use app\admin\validate\CreateStaffInfo as CreateStaffInfoValidate;
use app\admin\validate\EditStaffInfo as EditStaffInfoValidate;
use app\shop\validate\EditStaffAuthn as EditStaffAuthnValidate;
use app\shop\validate\EditStaffTrain as EditStaffTrainValidate;
use app\common\enum\Response;
use app\common\enum\staff\Authn;
use app\common\service\StaffCacheService;
use app\common\service\StaffService;
use app\shop\validate\SubmitIdCard;

class ShopStaff extends Base
{
    public function getShopService(): Json
    {
        $shopId = $this->param['shop_id'] ?? 0;
        if ($shopId == 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "缺少shop_id参数"));
        }
        $service = new StaffService();
        return json(success($service->getShopServiceById($shopId)));
    }

    public function add(): Json
    {
        $validate = new CreateStaffInfoValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->create($this->param));
    }

    public function edit(): Json
    {
        $validate = new EditStaffInfoValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $this->param['id'] = $this->param['staff_id'];
        $this->param['audit_status'] = 1;
        $this->param['audit_reason'] = "管理人员编辑，自动审核通过";
        $service = new StaffService();
        return json($service->edit($this->param));
    }


    // public function addAuthn(): Json
    // {
    //     $validate = new EditStaffAuthnValidate();
    //     $result = $validate->check($this->param);
    //     if (!$result) {
    //         return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
    //     }
    //     if ($this->param['staff_id'] == 0) {
    //         return json(fail(Response::REQUEST_PARAM_ERROR, "请选择员工"));
    //     }
    //     $service = new StaffService();
    //     return json($service->addAuthImage($this->param));
    // }

    // public function editAuthn(): Json
    // {
    //     $validate = new EditStaffAuthnValidate();
    //     $result = $validate->check($this->param);
    //     if (!$result) {
    //         return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
    //     }
    //     if ($this->param['staff_id'] == 0) {
    //         return json(fail(Response::REQUEST_PARAM_ERROR, "请选择员工"));
    //     }
    //     $service = new StaffService();
    //     return json($service->editAuthImage($this->param));
    // }

    // public function deleteAuthn(): Json
    // {
    //     if ($this->param['staff_id'] == 0) {
    //         return json(fail(Response::REQUEST_PARAM_ERROR, "请选择员工"));
    //     }
    //     $service = new StaffService();
    //     return json($service->delAuthImage($this->param));
    // }

    // 身份证提交
    public function addIdCard():Json{
        $validate = new SubmitIdCard();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $msg = "";
        $service = new StaffService();
        $res = $service->addIdCard($this->param);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 健康证提交
    public function addHealth():Json{
        $validate = new EditStaffAuthnValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        if ($this->param['staff_id'] == 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "请选择员工"));
        }
        $service = new StaffService();
        return json($service->addHealth($this->param));
    }

    // 技能证提交
    public function addSkill():Json{
        $validate = new EditStaffAuthnValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        if ($this->param['staff_id'] == 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "请选择员工"));
        }
        $service = new StaffService();
        return json($service->addSkill($this->param));
    }

    // 疫苗接种单提交
    public function addVaccination():Json{
        $validate = new EditStaffAuthnValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        if ($this->param['staff_id'] == 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "请选择员工"));
        }
        $service = new StaffService();
        return json($service->addVaccination($this->param));
    }

    // 无犯罪记录提交
    public function addNoCrime():Json{
        $validate = new EditStaffAuthnValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        if ($this->param['staff_id'] == 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "请选择员工"));
        }
        $service = new StaffService();
        return json($service->addNoCrime($this->param));
    }

    // 体检报告提交
    public function addPhysical():Json{
        $validate = new EditStaffAuthnValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        if ($this->param['staff_id'] == 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "请选择员工"));
        }
        $service = new StaffService();
        return json($service->addPhysical($this->param));
    }

    public function addTrain(): Json
    {
        $validate = new EditStaffTrainValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        if ($this->param['staff_id'] == 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "请选择员工"));
        }
        $service = new StaffService();
        return json($service->addTrain($this->param));
    }

    public function editTrain(): Json
    {
        $validate = new EditStaffTrainValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        if ($this->param['staff_id'] == 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "请选择员工"));
        }
        $service = new StaffService();
        return json($service->editTrain($this->param));
    }

    public function deleteTrain(): Json
    {
        if ($this->param['staff_id'] == 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "请选择员工"));
        }
        $service = new StaffService();
        return json($service->delTrain($this->param));
    }
}
