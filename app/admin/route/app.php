<?php

use think\facade\Route;

// 黑名单ip管理
// Route::middleware(\app\middleware\Blacklist::class);

// auth
Route::middleware(\app\admin\middleware\Auth::class);

Route::post('login', 'Login/index')->name('adminLogin');
Route::get('logout', 'Login/logout')->name('adminLogout');
Route::put('reset', 'Login/resetPassword')->name('adminResetPassword');
Route::get('log/index', 'AdminLog/index')->name('adminLog');
Route::get('detail', 'Login/detail')->name('adminLoginDetail');
// 新增权限或菜单
Route::post('rule', 'AuthRule/add')->name('ruleAdd');
// 修改权限或菜单
Route::put('rule/:id', 'AuthRule/edit')->name('ruleEdit');
// 删除权限或菜单
Route::delete('rule/:id', 'AuthRule/delete')->name('ruleDel');
// 获取权限或菜单列表
Route::get('rule', 'AuthRule/index')->name('ruleList');
// 获取权限或菜单详情
Route::get('rule/:id', 'AuthRule/detail')->name('ruleDetail');
// 新增角色
Route::post('role', 'AuthGroup/add')->name('groupAdd');
// 修改角色
Route::put('role/:id', 'AuthGroup/edit')->name('groupEdit');
// 删除角色
Route::delete('role/:id', 'AuthGroup/delete')->name('groupDel');
// 获取角色列表
Route::get('role', 'AuthGroup/index')->name('groupList');
// 获取角色详情
Route::get('role/:id', 'AuthGroup/detail')->name('groupDetail');
// 角色授权
Route::put('role/:id/permission', 'AuthGroup/group_rule')->name('groupRule');
// 新增管理员
Route::post('admin', 'Admin/add')->name('adminAdd');
// 修改管理员
Route::put('admin/:id', 'Admin/edit')->name('adminEdit');
// 删除管理员
Route::delete('admin/:id', 'Admin/delete')->name('adminDel');
// 获取管理员列表
Route::get('admin', 'Admin/index')->name('adminList');
// 获取管理员详情
Route::get('admin/:id', 'Admin/detail')->name('adminDetail');
// 生成小程序码
Route::get('shop/mini/qrcode', 'shop/getMiniCode')->name('shopMiniQrcode');
// 获取门店详情
Route::get('shop', 'Shop/detail')->name('shopDetail');
// 门店编辑
Route::put('shop', 'Shop/edit')->name('shopEdit');
// 门店banner
Route::get('shop/banner', 'Shop/getBanner')->name('shopBanner');
// 门店banner编辑
Route::put('shop/banner/:id', 'Shop/editBanner')->name('shopBannerEdit');
// 门店banner删除
Route::delete('shop/banner/:id', 'Shop/deleteBanner')->name('shopBannerDel');
// 门店banner添加
Route::post('shop/banner', 'Shop/addBanner')->name('shopBannerAdd');
// 添加共享配置
Route::post('shop/share', 'Shop/addShare')->name('shopShareAdd');
// 添加共享配置
Route::post('shop/share_all', 'Shop/addShareAll')->name('shopShareAddAll');
// 编辑共享配置
Route::put('shop/share/:id', 'Shop/editShare')->name('shopShareEdit');
// 删除共享配置
Route::delete('shop/share/:id', 'Shop/deleteShare')->name('shopShareDel');
// 获取共享配置
Route::get('shop/share', 'Shop/getShare')->name('shopShare');
// 门店结算
Route::get('shop/settlement', 'Shop/getSettlement')->name('shopSettlement');
// 门店结算-2
Route::get('shop/settlement/detail', 'Shop/getSettlementDetail')->name('shopSettlementDetail');
// 结算支付列表
Route::get('shop/settlement/pay', 'Shop/paySettlement')->name('shopSettlementPay');
// 支付确认按钮
Route::put('shop/settlement/pay', 'Shop/paySettlementConfirm')->name('shopSettlementPayConfirm');
// 搜索可共享员工列表
Route::get('share/staff/search', 'ShareStaff/search')->name('shareStaffSearch');
// 提交申请共享员工
Route::post('share/staff', 'ShareStaff/add')->name('shareStaffAdd');
// 查看我的申请
Route::get('share/staff/my', 'ShareStaff/my')->name('shareStaffMy');
// 查看所有向我申请的列表
Route::get('share/staff/to-me', 'ShareStaff/toMe')->name('shareStaffToMe');
// 审核共享员工
Route::put('share/staff/:id/audit', 'ShareStaff/audit')->name('shareStaffAudit');
// 关闭共享员工
Route::put('share/staff/:id/close', 'ShareStaff/close')->name('shareStaffClose');
// 删除共享员工
Route::delete('share/staff/:id', 'ShareStaff/del')->name('shareStaffDelete');
// 佣金账单
Route::get('share/staff/commission_bill', 'ShareStaff/getCommissionBill')->name('shareStaffCommissionBill');
Route::get('share/staff/commission_bill/detail', 'ShareStaff/getCommissionBillDetail')->name('shareStaffCommissionBillDetail');
// 获取共享员工列表
Route::get('share/staff/list', 'ShareStaff/list')->name('shareStaffList');
// 获取共享员工详情
Route::get('share/staff/:id', 'ShareStaff/detail')->name('shareStaffDetail');
// 获取共享员工订单列表
Route::get('share/staff/:id/orders', 'ShareStaff/getOrderList')->name('shareStaffOrderList');
Route::get('share/staff/:id/order/detail', 'ShareStaff/getOrderDetail')->name('shareStaffOrderDetail');
// 标签列表
Route::get('tag', 'Tag/tagList')->name('tagList');
Route::post('staff/tag', 'Tag/addStaffTag')->name('addStaffTag');
// 工种的设置
Route::put('staff/worker', 'Staff/setWorker')->name('setStaffWorker');
// 获取工作日历
Route::get('calendar', 'Staff/getWorkCalendar')->name('calendarList');
// 员工列表
Route::get('staff', 'Staff/index')->name('staffList');
Route::get('staff/search_name', 'Staff/getStaffBySearchName')->name('staffSearchName');
// 获取佣金流水
Route::get('staff/commission', 'Staff/getCommissionList')->name('commissionList');
// 提现记录
Route::get('staff/cash', 'Cash/getList')->name('cashList');
// 提现同意(打钱吧兄弟)
Route::put('staff/cash/:id/agree', 'Cash/agree')->name('cashAgree');
// 提现拒绝(把钱加回帐户里)
Route::put('staff/cash/:id/refuse', 'Cash/refuse')->name('cashRefuse');
// 根据id获取员工详情
Route::get('staff/:id', 'Staff/detail')->name('staffDetail');
// 审核技师
Route::put('staff/:id/audit', 'Staff/audit')->name('staffAudit');
// 审核认证信息
Route::put('staff/authn/:id/audit', 'Staff/authnAudit')->name('staffAuthnAudit');
// 审核培训信息
Route::put('staff/train/:id/audit', 'Staff/trainAudit')->name('staffTrainAudit');
// 上传保险
Route::post('staff/:staff_id/insure', 'Staff/addInsure')->name('addInsure');
// 删除保险
Route::delete('staff/insure/:id', 'Staff/deleteInsure')->name('deleteInsure');
// 获取意见反馈
Route::get('feedback', 'Feedback/index')->name('feedbackList');
// 意见反馈已读
Route::put('feedback/:id/read', 'Feedback/read')->name('feedbackRead');
// 获取帮助中心
Route::get('help', 'Help/index')->name('helpList');
// 添加帮助中心
Route::post('help', 'Help/add')->name('helpAdd');
// 编辑帮助中心
Route::put('help/:id', 'Help/edit')->name('helpEdit');
// 删除帮助中心
Route::delete('help/:id', 'Help/delete')->name('helpDelete');
// 获取帮助中心详情
Route::get('help/:id', 'Help/detail')->name('helpDetail');
//商品分类管理,商品三级分类店铺列表
Route::get('goodsCategory', 'GoodsCategory/index')->name('goodsCategoryIndex');
//商品分类管理,添加申请
Route::post('addApply', 'GoodsCategory/addApply')->name('goodsCategoryAddApply');
//商品分类管理,获取服务区域列表
Route::get('getServiceZoneById/:id', 'GoodsCategory/getServiceZoneById')->name('goodsCategoryGetServiceZoneById');
//商品分类管理,申请列表
Route::get('applyList', 'GoodsCategory/getApplyList')->name('goodsCategoryGetApplyList');
//商品分类管理,项目列表
Route::get('projectList', 'GoodsCategory/getProjectList')->name('goodsCategoryGetProjectList');
//商品分类管理,修改第三级分类名称
Route::put('thirdCategoryName', 'GoodsCategory/updateCategoryName')->name('goodsCategoryUpdateCategoryName');
//商品分类管理,修改三级分类状态（上架或下架）
Route::put('thirdCategoryStatus', 'GoodsCategory/updateCategoryStatus')->name('goodsCategoryUpdateCategoryStatus');
//商品管理列表页
Route::get('goodsList', 'Goods/goodsIndex')->name('goodsIndex');
//商品管理获取配置项列表
Route::get('goodsConfigItems', 'Goods/goodsAddConfigItems')->name('goodsGoodsAddConfigItems');
//商品管理添加
Route::post('goods', 'Goods/createGoods')->name('goodsCreateGoods');
//商品管理详情
Route::get('goods/:id', 'Goods/goodsDetails')->name('goodsDetails');
//商品管理修改
Route::put('goods/:id', 'Goods/updateGoods')->name('goodsUpdateGoods');
//商品管理删除
Route::delete('goods/:id', 'Goods/deleteGoods')->name('goodsDeleteGoods');
//订单评论列表
Route::get('orderComment', 'OrderComment/index')->name('orderCommentIndex');
//订单评论审核
Route::post('orderComment/audit', 'OrderComment/audit')->name('orderCommentAudit');
// 订单支付
Route::get('order/pay_list', 'Order/payList')->name('orderPay');
// 订单列表
Route::get('orders', 'Order/index')->name('orderList');
// 完成服务
Route::put('order/finish', 'app\shop\controller\Order@finish')->name('finishOrder');
// 订单详情
Route::get('order/:order_id', 'Order/detail')->name('orderDetail');
// 获取服务流水记录
Route::get('order/:order_id/service_log', 'Order/getServiceLog')->name('orderServiceLog');
// 获取订单排班
Route::get('order/:order_id/schedule', 'Order/getSchedule')->name('orderSchedule');
Route::put('order/:order_id/schedule', 'Order/updateSchedule')->name('orderUpdateSchedule');
// 保存订单
Route::put('order/:order_id', 'Order/sysOperation')->name('orderSysOperation');
//订单日志
Route::get('order/:order_id/log', 'Order/getSysOperationLog')->name('getSysOperationLog');
// 上传合同
Route::post('order/:order_id/contract', 'Order/uploadContract')->name('uploadContract');
// 门店确认订单
Route::post('order/:order_id/confirm', 'Order/shopConfirm')->name('shopConfirmOrder');
// 面试列表
Route::get('interviews', 'Interview/index')->name('interviewList');
// 导入订单
Route::post('order/import', 'Order/importOrder')->name('orderImport');
// 用户申请退款列表
Route::get('refundApply', 'Order/refundApply')->name('orderRefundApply');
// 用户申请退款审核
Route::put('refundApplyAudit', 'Order/refundApplyAudit')->name('orderRefundApplyAudit');
// 获取mongo日志
Route::get('opslog', 'Help/getMongoLog')->name('getMongoLog');
// 会员管理
Route::get('member/index', 'Member/index')->name('memberIndex');
Route::get('member/detail', 'Member/detail')->name('memberDetail');
// 活动添加
Route::post('activity', 'Activity/create')->name('activityCreate');
// 活动列表
Route::get('activity', 'Activity/index')->name('activityIndex');
// 活动报名列表
Route::get('activity/signup', 'Activity/signupList')->name('activitySignupList');
// 活动详情
Route::get('activity/:id', 'Activity/detail')->name('activityDetail');
// 活动编辑
Route::put('activity/:id', 'Activity/update')->name('activityUpdate');
// 活动删除
Route::delete('activity/:id', 'Activity/softDelete')->name('activitySoftDelete');

// 服务人员api
// 获取归属门店信息
Route::get('shop/staff/shop', 'ShopStaff/getShopService')->name('getShopService');
// 新增门店服务人员
Route::post('shop/staff', 'ShopStaff/add')->name('shopStaffAdd');
// 编辑门店服务人员
Route::put('shop/staff', 'ShopStaff/edit')->name('shopStaffEdit');
// //认证证明
// Route::post('shop/staff/authn', 'ShopStaff/addAuthn')->name('addStaffAuthn');
// Route::put('shop/staff/:id/authn', 'ShopStaff/editAuthn')->name('editStaffAuthn');
// Route::delete('shop/staff/:id/authn', 'ShopStaff/deleteAuthn')->name('deleteStaffAuthn');
// 身份证提交
Route::post('shop/staff/idcard', 'ShopStaff/addIdCard')->name('addIdCard');
// 健康证提交
Route::post('shop/staff/health', 'ShopStaff/addHealth')->name('addHealth');
// 技能证提交
Route::post('shop/staff/skill', 'ShopStaff/addSkill')->name('addSkill');
// 技能证编辑
Route::post('shop/staff/skill/:id', 'ShopStaff/editSkill')->name('editSkill');
// 技能证删除
Route::delete('shop/staff/skill/:id', 'ShopStaff/deleteSkill')->name('deleteSkill');
// 疫苗接种单提交
Route::post('shop/staff/vaccination', 'ShopStaff/addVaccination')->name('addVaccination');
// 无犯罪记录提交
Route::post('shop/staff/no_crime', 'ShopStaff/addNoCrime')->name('addNoCrime');
// 体检报告提交
Route::post('shop/staff/physical', 'ShopStaff/addPhysical')->name('addPhysical');
// 体检报告编辑
Route::post('shop/staff/physical/:id', 'ShopStaff/editPhysical')->name('editPhysical');
// 体检报告删除
Route::delete('shop/staff/physical/:id', 'ShopStaff/deletePhysical')->name('deletePhysical');

// 培训经历
Route::post('shop/staff/train', 'ShopStaff/addTrain')->name('addTrain');
Route::put('shop/staff/train/:id', 'ShopStaff/editTrain')->name('editTrain');
Route::delete('shop/staff/train/:id', 'ShopStaff/deleteTrain')->name('deleteTrain');
// 服务人员api End

// 许愿池
Route::get('wishpool', 'WishPool/index')->name('wishPoolIndex');
Route::post('wishpool/audit', 'WishPool/audit')->name('wishPoolAudit');
Route::post('wishpool/award', 'WishPool/award')->name('wishPoolAward');
// 许愿池END

// 合同
// (门店)添加分账列表
Route::post('shop/add_receiver', 'Shop/addReceiver')->name('shopAddReceiver');
// (商品)绑定合同模版
Route::post('goods/bindContract', 'Goods/bindContract')->name('goodsBindContract');
// (商品)删除合同模版
Route::post('goods/delContract', 'Goods/deleteContract')->name('goodsDeleteContract');

// 选择所有审核通过的模版
Route::get('contractTemplate/audit', 'ContractTemplate/getAuditList')->name('contractTemplateAuditList');
// 新增模版
Route::post('contractTemplate', 'ContractTemplate/create')->name('contractTemplateCreate');
// 编辑模版
Route::put('contractTemplate/:id', 'ContractTemplate/update')->name('contractTemplateUpdate');
// 模版列表
Route::get('contractTemplate', 'ContractTemplate/index')->name('contractTemplateIndex');
// 删除模版
Route::delete('contractTemplate/:id', 'ContractTemplate/delete')->name('contractTemplateDelete');
// 获取详情
Route::get('contractTemplate/:id', 'ContractTemplate/detail')->name('contractTemplateDetail');

// 企业实名认证
Route::post('shop/enterpriseAuthn', 'Contract/enterpriseAuthn')->name('contractEnterpriseAuthn');
// 企业实名认证回调
Route::post('shop/enterpriseAuthn/callback', 'Contract/enterpriseAuthnCallback')->name('contractEnterpriseAuthnCallback');
// 上传公章
Route::post('shop/enterpriseAuthn/upload', 'Contract/uploadEnterpriseAuthn')->name('contractUploadEnterpriseAuthn');
// 合同END

// 获取节假日列表
Route::get('holiday', 'Holiday/index')->name('holidayIndex');
// 获取指定日期是否为节假日
Route::get('holiday/checkDate', 'Holiday/checkDate')->name('holidayCheckDate');

// 财务管理
// 订单基础数据更新
Route::put('finance/orderBasic', 'Finance/updateFinanceOrderBasic');
// 订单基础数据详情
Route::get('finance/orderBasic/:id', 'Finance/getFinanceOrderBasicById');
// 订单基础数据列表
Route::get('finance/orderBasic', 'Finance/getFinanceOrderBasic');

// 财务管理
// 获取店铺统计
Route::get('finance/shopStat', 'Finance/getShopStat');
// 分类（商品二级分类）占比统计
Route::get('finance/shopCategory', 'Finance/getShopCategoryStat');
// 应收账款统计
Route::get('finance/shopReceivable', 'Finance/shopReceivableStat');
// 订单基础数据列表（ByShopId）
Route::get('finance/orderBasic', 'Finance/getFinanceOrderBasic');
// 订单基础数据详情
Route::get('finance/orderBasic/:id', 'Finance/getFinanceOrderBasicById');
// 订单基础数据更新
Route::put('finance/orderBasic/:id', 'Finance/updateFinanceOrderBasic');
// 获取分类销售统计数据
Route::get('finance/shopCategoryStat', 'Finance/getFinanceShopCategoryStat');
// 获取订单尾款统计数据
Route::get('finance/shopReceivableStat', 'Finance/getFinanceShopReceivableStat');

// 抽奖活动配置
// 创建抽奖活动奖品
Route::post('lottery/prize', 'Lottery/PrizeCreate');
// 修改抽奖活动奖品
Route::put('lottery/prize/:id', 'Lottery/PrizeUpdate');
// 创建抽奖活动
Route::post('lottery', 'Lottery/create');
// 修改抽奖活动
Route::put('lottery/:id', 'Lottery/update');
// 获取抽奖活动列表
Route::get('lottery', 'Lottery/index');
// 获取中奖记录列表
Route::get('lottery/prizeRecord', 'Lottery/prizeRecord');
// 后台发放奖品(核销)
Route::put('lottery/prizeRecord/send', 'Lottery/prizeRecordUpdate');
// 获取抽奖活动详情
Route::get('lottery/:id', 'Lottery/detail');
// 删除抽奖活动奖品
Route::delete('lottery/prize/:id', 'Lottery/PrizeDelete');
// 删除抽奖活动
Route::delete('lottery/:id', 'Lottery/delete');
// 抽奖活动配置 end

//优惠券模板添加
Route::post('couponTemplate', 'Coupon/createCouponTemplate')->name('createCouponTemplate');
//优惠券模板列表
Route::get('couponTemplate', 'Coupon/couponTemplateList')->name('couponTemplateList');
//优惠券模板更新
Route::put('couponTemplate/:id', 'Coupon/updateCouponTemplate')->name('updateCouponTemplate');
//优惠券模板详情
Route::get('couponTemplate/:id', 'Coupon/getCouponTemplateById')->name('getCouponTemplateById');
//优惠券模板删除
Route::delete('couponTemplate/:id', 'Coupon/deleteCouponTemplate')->name('deleteCouponTemplate');
//优惠券列表
Route::get('coupon', 'Coupon/couponList')->name('couponList');
//优惠券详情
Route::get('coupon/:id', 'Coupon/getCouponById')->name('getCouponById');