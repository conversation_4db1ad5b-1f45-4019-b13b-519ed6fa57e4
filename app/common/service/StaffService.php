<?php

namespace app\common\service;

use app\common\enum\Response;
use app\common\enum\staff\Authn;
use app\common\enum\staff\Constellation;
use app\common\enum\staff\Cook;
use app\common\enum\staff\Degree;
use app\common\enum\staff\Language;
use app\common\enum\staff\Nation;
use app\common\enum\staff\Worker;
use app\common\enum\staff\Zodiac;
use app\common\model\AuditModel;
use app\common\model\CommissionDetailModel;
use app\common\model\GoodsCategoryBasicModel;
use app\common\model\GoodsCategoryModel;
use app\common\model\InterviewScheduleModel;
use app\common\model\OrderCommentModel;
use app\common\model\OrderOccupancyModel;
use app\common\model\OrdersModel;
use app\common\model\OrderStaffRelModel;
use app\common\model\StaffAccountModel;
use app\common\model\StaffAuthnModel;
use app\common\model\StaffCommentModel;
use app\common\model\StaffInsureModel;
use app\common\model\StaffModel;
use app\common\model\StaffRegionModel;
use app\common\model\StaffReposeCacheModel;
use app\common\model\StaffReposeModel;
use app\common\model\StaffServiceModel;
use app\common\model\StaffTagModel;
use app\common\model\StaffTrainingModel;
use app\common\model\StaffWorkerModel;
use app\common\model\TagModel;
use GoodsCategoryBasic;
use think\facade\Db;
use think\facade\Event;

class StaffService
{
    public static $basicColumns = [
        'id',
        'shop_id',
        'mobile',
        'real_name',
        'province',
        'city',
        'area',
        'address',
        'gender', // 性别
        'birthday',
        'avatar',
        'introduce',
        'native_province',
        'native_city',
        'start_work_year',
        'language',
        'cooking_skill',
        'nation',
        'height',
        'zodiac',
        'constellation',
        'education',
        'video_introduce',
        'level_id',
        'all_region',
        'bank_num',
        'bank_user',
        'bank_name',
        'photo_wall', //照片墙
        'remark', // 备注
        'skill_total', // 技能证书总数
        'status',
        'audit_status',
        'audit_reason',
        'is_super',
    ];

    public static $listColumns = [
        'id',
        'shop_id',
        'mobile',
        'real_name',
        'gender', // 性别
        'avatar',
        'level_id',
        'start_work_year',
        'native_province',
        'native_city',
        'education',
        'birthday',
        'status',
        'audit_status',
        'register_time',
    ];

    public function getStaffData(int $id): array
    {
        $with  = StaffModel::getWith();
        $staff = StaffModel::where('id', $id)
            ->field(self::$basicColumns)
            ->with(array_keys($with))->find();

        if (!$staff) {
            return [];
        }
        $this->formatStaffData($staff, array_keys($with));
        return $staff
            ->hidden(array_keys($with))
            ->visible(array_filter($with))
            ->toArray();
    }

    // 获取详情
    public function getStaff(int $id): array
    {
        $res = (new StaffCacheService($id))->getData();
        if (!empty($res)) {
            $res['balance']         = "0.000";
            $res['commission']      = "0.000";
            $res['start_work_year'] = (string)$res['start_work_year']; //type Int to String
            $account                = StaffAccountModel::where('staff_id', $id)->findOrEmpty();
            if (!$account->isEmpty()) {
                $res['balance']    = $account->balance;
                $res['commission'] = $account->commission;
            }
            $shopId = $res['staff_shop']['id'] ?? 0;
            if ($res['is_super'] == 1 && $shopId > 0) {
                // 获取订单数量
                $res['order_total'] = OrdersModel::where('shop_id', $shopId)->field('id')->count();
                // 获取面试数量
                $res['interview_total'] = InterviewScheduleModel::where('shop_id', $shopId)->field('id')->count();
                // 获取已经评论的数量
                $res['order_commented_total'] = OrderCommentModel::where(['shop_id' => $shopId, 'audit_status' => 1])->field('id')->count();
            } else {
                // 获取订单数量
                $res['order_total'] = OrderStaffRelModel::where('staff_id', $id)->field('id')->count();
                // 获取面试数量
                $res['interview_total'] = InterviewScheduleModel::where('interviewer_id', $id)->field('id')->count();
                // 获取已经评论的数量
                $res['order_commented_total'] = OrderCommentModel::where(['staff_id' => $id, 'audit_status' => 1])->field('id')->count();
            }
            return success($res);
        }
        return fail(Response::ACCOUNT_FORBIDDEN, '信息不存在');
    }

    // 获取列表
    public function getList(array $params = []): array
    {
        $where = [];
        // 门店搜索
        if (isset($params['shop_id']) && $params['shop_id'] > 0) {
            array_push($where, ['shop_id', '=', intval($params['shop_id'])]);
        }

        if (isset($params['status']) && intval($params['status']) >= 0) {
            array_push($where, ['status', '=', intval($params['status'])]);
        }

        //审核状态1待审核2已审核3审核不通过
        if (isset($params['check_status']) && intval($params['check_status']) >= 0 && in_array($params['check_status'], [StaffModel::CHECK_STATUS_WAIT, StaffModel::CHECK_STATUS_SUCCESS, StaffModel::CHECK_STATUS_FAIL])) {
            array_push($where, ['audit_status', '=', intval($params['check_status'])]);
        }

        //搜索名字
        if (isset($params['search_name']) && !empty($params['search_name'])) {
            array_push($where, ['real_name', 'like', '%' . $params['search_name'] . '%']);
        }

        //搜索手机号
        if (isset($params['search_mobile']) && !empty($params['search_mobile'])) {
            array_push($where, ['mobile', '=', $params['search_mobile']]);
        }

        $list = StaffModel::where($where)
            ->field(self::$listColumns)
            ->order('id desc')
            ->paginate(config('admin.limit'))
            ->toArray();

        if ($list['total'] > 0) {
            $rows = [];
            foreach ($list['data'] as $key => $val) {
                $staff = (new StaffCacheService($val['id']))->getData();
                // 追加参数
                $staff['register_time'] = $val['register_time'] ? date('Y-m-d H:i:s', $val['register_time']) : '';
                $rows[]                 = $staff;
            }
            $list['data'] = $rows;
        }
        return $list;
        // $data = [];
        // foreach ($list as &$item) {
        // }

        // $with = StaffModel::getWith(['relAvatar', 'relShop', 'relAccount']);
        // $list = StaffModel::where($where)
        //     ->field(self::$listColumns)
        //     ->with(array_keys($with))
        //     ->order('id desc')
        //     ->paginate(config('admin.limit'));


        // if (!$list->isEmpty()) {
        //     foreach ($list as &$item) {
        //         $this->formatStaffData($item, array_keys($with));
        //     }
        //     unset($item);
        // }
        // return $list->visible(array_filter($with))->toArray();
    }

    public function getShopStaff(array $params = []): array
    {
        $where = [];
        // 门店id
        if (isset($params['shop_id']) && !empty($params['shop_id'])) {
            array_push($where, ['shop_id', '=', intval($params['shop_id'])]);
        }
        //搜索名字
        if (isset($params['search_name']) && !empty($params['search_name'])) {
            array_push($where, ['real_name', 'like', '%' . $params['search_name'] . '%']);
        }
        //搜索手机号
        if (isset($params['search_mobile']) && !empty($params['search_mobile'])) {
            array_push($where, ['mobile', '=', $params['search_mobile']]);
        }
        return StaffModel::where($where)
            ->field('id,real_name')
            ->order('id desc')
            ->select()
            ->toArray();
    }

    public function getStaffShopById(int $id): bool|array
    {
        $staff = StaffModel::where('id', $id)->field(self::$basicColumns)->findOrEmpty();
        if ($staff->isEmpty()) {
            return false;
        }

        $shop = $staff->relShop;
        if (!$shop) {
            return false;
        }
        $shopService = (new GoodsCategoryService())->getThirdCategoryListById($staff->shop_id);
        if ($shopService) {
            $shop->shop_service = changeEnumKeyValue(array_column($shopService, 'third_category_unique_name', 'c_id'));
        }
        return $shop->toArray();
    }

    public function getShopServiceById(int $id): bool|array
    {
        $shopService = (new GoodsCategoryService())->getThirdCategoryListById($id);
        if ($shopService) {
            return changeEnumKeyValue(array_column($shopService, 'third_category_unique_name', 'c_id'));
        }
        return [];
    }

    // 处理单例, with加载关系数据
    public static function formatStaffData(&$row, $with = [])
    {
        // 放到外面不做缓存
        // $row->balance = "0.000";
        // $row->commission = "0.000";
        // if (in_array('relAccount', $with) && !empty($row->relAccount)) {
        //     $row->balance = $row->relAccount->balance;
        //     $row->commission = $row->relAccount->commission;
        // }
        if (in_array('relService', $with)) {
            $row->staff_service = changeEnumKeyValue(array_column($row->relService->toArray(), 'third_category_unique_name', 'c_id'));
        }

        if (in_array('relShop', $with)) {
            $row->staff_shop = $row->relShop;
        }

        if (isset($row->address)) {
            $address = '';
            if ($row->area > 0) {
                $regionService = new RegionService();
                $cacheAddress  = $regionService->getRegionDetail($row->area);
                $str           = $cacheAddress['city_name'] . $cacheAddress['name'];
                if ($cacheAddress['province_name'] != $cacheAddress['city_name']) {
                    $address = $cacheAddress['province_name'] . $str;
                } else {
                    $address = $str;
                }
            }
            $row->format_address = $address . $row->address; // 格式化地址
        }

        // 标签
        if (in_array('relTag', $with) && !empty($row->relTag)) {
            $tag = [];
            foreach ($row->relTag as $item) {
                if ($item->show_status != 10) {
                    continue;
                }
                if ($item->is_delete == 1) {
                    continue;
                }
                $tag[] = [
                    'id'           => $item['pivot']['id'],
                    'type_id'      => $item['type_id'],
                    'tag_id'       => $item['id'],
                    'tag_name'     => $item['name'],
                    'flag'         => $item['flag'],
                    'image_id'     => $item['image_id'],
                    'format_image' => $item['flag'] == 2 ? UploadService::get($item['image_id']) : '',
                    // 'is_permanent' => $item['pivot']['is_permanent'],
                    // 'start_time' => $item['pivot']['is_permanent'] == 0 ? date('Y-m-d', $item['pivot']['start_time']) : 0,
                    // 'end_time' => $item['pivot']['is_permanent'] == 0 ? date('Y-m-d', $item['pivot']['end_time']) : 0,
                    // 'sort' => $item['pivot']['sort'],
                    'status'       => $item['show_status'],
                ];
            }
            $row->staff_tag = $tag;
        }

        // 职业
        if (in_array('relWorker', $with) && !empty($row->relWorker)) {
            $worker     = [];
            $sortAsc    = [];
            $workerCode = Worker::WorkerCode();
            foreach ($row->relWorker as $val) {
                array_push($worker, [
                    'id'          => $val['id'],
                    'staff_id'    => $val['staff_id'],
                    'worker_id'   => $val['worker_id'],
                    'worker_name' => $workerCode[$val['worker_id']] ?? '',
                    'sort'        => $val['sort'],
                ]);
                array_push($sortAsc, $val['sort']);
            }
            array_multisort($sortAsc, SORT_ASC, $worker);
            $row->staff_worker = $worker;
        }

        if (isset($row->gender)) {
            $row->format_gender = $row->gender == 1 ? '男' : '女'; // 格式化性别
        }

        if (in_array('relAvatar', $with) && !empty($row->relAvatar)) {
            if ($row->relAvatar->storage == 'remote') {
                $formatAvatar = $row->relAvatar->file_url;
            } else {
                $formatAvatar = config('upload.' . $row->relAvatar->storage . '.domain') . $row->relAvatar->file_url;
            }
            $row->format_avatar = formatAvatar($formatAvatar); // 格式化头像
        }

        if (isset($row->native_city)) {
            //格式化籍贯
            $regionService = new RegionService();
            $nativeCache   = $regionService->getRegionDetail($row->native_city);
            if (empty($nativeCache)) {
                $row->format_native = '未知';
            } else {
                $row->format_native = $nativeCache['province_name'] != $nativeCache['city_name'] ? $nativeCache['province_name'] . $nativeCache['city_name'] : $nativeCache['city_name'];
            }
        }
        if (isset($row->start_work_year)) {
            // 现在年份-开始工作年份= 工作年限
            $row->work_age = date('Y') - $row->start_work_year;
        }

        if (isset($row->language)) {
            // 格式化语言
            $languages   = array_filter(array_unique(explode(',', $row->language)));
            $languageStr = [];
            if ($languages) {
                foreach ($languages as $language) {
                    $languageStr[] = Language::LanguageCode[$language];
                }
            }
            $row->format_language = $row->language ? implode(',', $languageStr) : '';
        }

        if (isset($row->cooking_skill)) {
            // 格式化烹饪技能
            $cooks   = array_filter(array_unique(explode(',', $row->cooking_skill)));
            $cookStr = [];
            foreach ($cooks as $cook) {
                $cookStr[] = Cook::CookCode[$cook];
            }
            $row->format_cooking_skill = $row->cooking_skill ? implode(',', $cookStr) : '';
        }

        if (isset($row->nation)) {
            $row->format_nation = $row->nation ? Nation::NationCode[$row->nation] : '未知';
        }

        if (isset($row->zodiac)) {
            $row->format_zodiac = $row->zodiac ? Zodiac::ZodiacCode[$row->zodiac] : '未知';
        }

        if (isset($row->constellation)) {
            $row->format_constellation = $row->constellation ? Constellation::ConstellationCode[$row->constellation] : '未知';
        }

        if (isset($row->education)) {
            $row->format_education = $row->education ? Degree::DegreeCode[$row->education] : '未知';
        }

        if (isset($row->status)) {
            $row->format_status = isset($row->status) ? StaffModel::STATUS_TEXT[$row->status] : '未知';
        }

        if (isset($row->audit_status)) {
            $row->format_audit_status = isset($row->audit_status) ? StaffModel::CHECK_STATUS_TEXT[$row->audit_status] : '未知';
        }

        if (isset($row->photo_wall) && !empty($row->photo_wall)) {
            // 照片墙
            $wall     = [];
            $wallIds  = explode(',', $row->photo_wall);
            $wallFile = UploadService::getByIds($wallIds);
            foreach ($wallIds as $key => $item) {
                if (!isset($wallFile[$item])) {
                    continue;
                }
                $item = [
                    'image_id'     => $item,
                    'format_image' => $wallFile[$item],
                ];
                array_push($wall, $item);
            }
            $row->photo_wall = $wall;
        }

        if (in_array('relAuthn', $with) && !empty($row->relAuthn)) {
            // 认证证明
            $file      = UploadService::getByIds(array_column($row->relAuthn->toArray(), 'image_id'));
            $authnList = [];
            foreach ($row->relAuthn as $item) {
                $key               = Authn::AuthnKey[$item->authn_type];
                $authnList[$key][] = [
                    'id'                => $item->id,
                    'name'              => $item->name,
                    'authn_no'          => $item->authn_no,
                    'audit_status'      => $item->audit_status,
                    'audit_reason'      => $item->audit_reason,
                    'audit_time'        => $item->audit_time,
                    'image_id'          => $item->image_id,
                    'format_authn_type' => Authn::AuthnCode[$item->authn_type],
                    'format_image'      => formatLogo($file[$item->image_id] ?? ''),
                    'format_status'     => StaffAuthnModel::AUDIT_STATUS[$item->audit_status] ?? ''
                ];
            }
            unset($item);
            foreach ($authnList as $key => $item) {
                $row->$key = $item;
            }
        }

        if (in_array('relRegion', $with) && !empty($row->relRegion)) {
            // 获取不接单时间
            $row->repose = self::buildRepose($row->relRepose->toArray());
        }

        if (in_array('relRegion', $with) && !empty($row->relRegion)) {
            // 获取接单区域
            $row->region = self::buildArea($row->relRegion->toArray());
        }

        if (in_array('relTrain', $with) && !empty($row->relTrain)) {
            // 培训信息
            $row->train = $row->relTrain;
        }

        if (in_array('relInsure', $with) && !empty($row->relInsure)) {
            // 保险信息
            $now = time();
            foreach ($row->relInsure as &$item) {
                $item->format_image  = formatLogo(UploadService::get($item->image_id));
                $item->format_status = '正常';
                // 即将到期
                if ($item['end_time'] - $now <= (10 * 24 * 3600)) {
                    // 状态
                    $item->format_status = '即将到期';
                }
                // 未开始
                if ($item['start_time'] > $now) {
                    $item->format_status = '等待开始';
                }
                $item->format_start = date('Y-m-d', $item['start_time']);
                $item->format_end   = date('Y-m-d', $item['end_time']);
            }
            unset($item);
            $row->insure = $row->relInsure;
        }
    }

    public function create(array $param = []): array
    {
        // 开始事务
        Db::startTrans();
        try {
            $id = StaffModel::insertGetId([
                'shop_id'       => $param['shop_id'],
                'mobile'        => $param['mobile'],
                'password'      => makePassword($param['passwd']),
                'register_time' => time(),
            ]);
            if ($id <= 0) {
                return fail(Response::REGISTER_FAIL, '新增服务人员失败');
            }
            //未后续做铺垫
            $param['staff_id'] = $id;
            $param['audit_status'] = 1;
            $param['audit_reason'] = "管理人员添加，自动审核通过";
            $data = $this->filterParam($param);
            if (empty($data)) {
                return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
            }
            $res = StaffModel::where('id', $id)->update($data);
            // 添加审核流水
            AuditService::addAuditLog(intval($param['admin_id']), AuditModel::USER_ROLE_TYPE_SHOP, AuditModel::TYPE_STAFF, $id, intval($param['audit_status']), $param['audit_reason'] ?? '');
            if (!$res) {
                return fail(Response::ACCOUNT_EDIT_FORBIDDEN, '编辑失败');
            }
            // 验证语言
            if (isset($param['languages'])) {
                $languages = array_unique(explode(',', $param['languages']));
                Event::trigger('StaffLanguage', ['staff_id' => $id, 'language' => $languages]);
            }
            // 验证烹饪技能
            if (isset($param['cooks'])) {
                $cooks = array_unique(explode(',', $param['cooks']));
                Event::trigger('StaffCookingSkill', ['staff_id' => $id, 'cooking_skill' => $cooks]);
            }
            // 服务设置
            $this->setService($param);
            // 设置工种
            $this->setWorker($param);
            // 认证图片合并审核
            $res = StaffAuthnModel::where('staff_id', $id)->update([
                'audit_status'  => intval($param['audit_status']),
                'audit_reason'  => $param['audit_reason'],
                'audit_time'    => time(),
                'audit_user_id' => intval($param['admin_id']),
            ]);
            // 添加审核流水
            AuditService::addAuditLog(intval($param['admin_id']), AuditModel::USER_ROLE_TYPE_SHOP, AuditModel::TYPE_STAFF_AUTHN, $id, intval($param['audit_status']), $param['audit_reason'] ?? '');
            // 提交事务
            Db::commit();
            // 统计技能证书数量
            Event::trigger('StaffSkillTotal', $id);
            // 更新缓存
            (new StaffCacheService($id))->getData(true);
            return $this->getStaff($id);
        } catch (\Exception $e) {
            echo $e->getMessage();
            // 回滚事务
            Db::rollback();
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN, '编辑失败');
        }
    }

    // 编辑
    public function edit(array $param = []): array
    {
        // 开始事务
        Db::startTrans();
        try {
            $id = intval($param['id']);
            if ($id <= 0) {
                return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
            }
            $data = $this->filterParam($param);
            if (empty($data)) {
                return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
            }
            $res = StaffModel::where('id', $id)->update($data);
            // 添加审核流水
            if (isset($param['admin_id'])) {
                AuditService::addAuditLog(intval($param['admin_id']), AuditModel::USER_ROLE_TYPE_SHOP, AuditModel::TYPE_STAFF, $id, intval($param['audit_status']), $param['audit_reason'] ?? '');
            }
            if (!$res) {
                return fail(Response::ACCOUNT_EDIT_FORBIDDEN, '编辑失败-1');
            }

            // 验证语言
            if (isset($param['languages'])) {
                $languages = array_unique(explode(',', $param['languages']));
                Event::trigger('StaffLanguage', ['staff_id' => $id, 'language' => $languages]);
            }

            // 验证烹饪技能
            if (isset($param['cooks'])) {
                $cooks = array_unique(explode(',', $param['cooks']));
                Event::trigger('StaffCookingSkill', ['staff_id' => $id, 'cooking_skill' => $cooks]);
            }

            // 服务设置
            $this->setService($param);

            if (isset($param['admin_id']) && intval($param['admin_id']) > 0) {
                // 设置工种
                $this->setWorker($param);
                // 认证图片合并审核
                $res = StaffAuthnModel::where('staff_id', $id)->update([
                    'audit_status'  => intval($param['audit_status']),
                    'audit_reason'  => $param['audit_reason'],
                    'audit_time'    => time(),
                    'audit_user_id' => intval($param['admin_id']),
                ]);
                // 添加审核流水
                AuditService::addAuditLog(intval($param['admin_id']), AuditModel::USER_ROLE_TYPE_SHOP, AuditModel::TYPE_STAFF_AUTHN, $id, intval($param['audit_status']), $param['audit_reason'] ?? '');
                // 提交事务
                Db::commit();
                // 统计技能证书数量
                Event::trigger('StaffSkillTotal', $id);
            } else {
                // 提交事务
                Db::commit();
            }
            // 更新缓存
            (new StaffCacheService($id))->getData(true);
            return $this->getStaff($id);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN, '编辑失败-2');
        }
    }

    // 过滤参数
    private function filterParam($param)
    {
        $res = [];
        // 门店绑定不允许修改
        // if (isset($param['shop_id'])) {
        //     $res['shop_id'] = $param['shop_id'];
        // }
        if (isset($param['real_name'])) {
            $res['real_name'] = $param['real_name'];
        }
        if (isset($param['gender'])) {
            $res['gender'] = intval($param['gender']);
        }
        if (isset($param['nation'])) {
            $res['nation'] = intval($param['nation']);
        }
        if (isset($param['address'])) {
            $res['province'] = intval($param['address_province']);
            $res['city']     = intval($param['address_city']);
            $res['area']     = intval($param['address_district']);
            $res['address']  = $param['address'];
        }
        if (isset($param['height'])) {
            $res['height'] = $param['height'];
        }
        // 获取生肖和星座
        if (isset($param['birthday'])) {
            $res['birthday']      = $param['birthday'];
            $birthday             = explode('-', $param['birthday']);
            $res['zodiac']        = intval((($birthday[0] - 1900) % 12) + 1);
            $res['constellation'] = Constellation::getConstellation($birthday[1], $birthday[2]);
        }
        if (isset($param['image_id'])) {
            $res['avatar'] = $param['image_id'];
        }
        if (isset($param['native_province']) && isset($param['native_city'])) {
            $res['native_province'] = $param['native_province'];
            $res['native_city']     = $param['native_city'];
        }

        // 验证语言
        if (isset($param['languages'])) {
            $languages       = array_unique(explode(',', $param['languages']));
            $res['language'] = implode(',', $languages);
        }

        // 验证烹饪技能
        if (isset($param['cooks'])) {
            $cooks                = array_unique(explode(',', $param['cooks']));
            $res['cooking_skill'] = implode(',', $cooks);
        }
        if (isset($param['degree'])) {
            $res['education'] = intval($param['degree']);
        }
        if (isset($param['start_work'])) {
            $res['start_work_year'] = $param['start_work'];
        }

        //video_introduce视频介绍
        if (isset($param['video_url'])) {
            $res['video_introduce'] = $param['video_url'];
        }

        //自我介绍
        if (isset($param['remark'])) {
            $res['remark'] = trim($param['remark']);
        }

        // photo_wall 照片墙
        if (isset($param['photo_wall'])) {
            $res['photo_wall'] = $param['photo_wall'];
        }

        // 银行卡信息
        if (isset($param['bank_num']) && isset($param['bank_user']) && isset($param['bank_name'])) {
            $res['bank_num']  = $param['bank_num'];
            $res['bank_user'] = $param['bank_user'];
            $res['bank_name'] = $param['bank_name'];
        }

        if (isset($param['admin_id']) && intval($param['admin_id']) > 0) {
            // 门店操作不修改审核状态
            if (isset($param['audit_status'])) {
                $res['audit_status']  = intval($param['audit_status']);
                $res['audit_reason']  = $param['audit_reason'];
                $res['audit_time']    = time();
                $res['audit_user_id'] = intval($param['admin_id']);
                if (intval($param['audit_status']) == Staffmodel::CHECK_STATUS_SUCCESS) {
                    $res['status'] = intval($param['status'] ?? 0);
                } else {
                    $res['status'] = 0;
                }
            }
            if (isset($param['level_id'])) {
                $res['level_id'] = intval($param['level_id']);
            }
        } else {
            // 用户修改，需要重新审核
            $res['audit_status']  = 0;
            $res['audit_reason']  = '';
            $res['audit_time']    = 0;
            $res['audit_user_id'] = 0;
            $res['status']        = 0; // 重新审核，需要重新上线
            $res['update_time']   = time();
        }
        return $res;
    }

    

    public function getRestTime($id = 0, StaffModel $staff = null): bool|array
    {
        if ($id > 0) {
            $staff = StaffModel::where('id', $id)->field(self::$basicColumns)->findOrEmpty();
        }
        if ($staff->isEmpty()) {
            return false;
        }
        return self::buildRepose($staff->relRepose->toArray());
    }

    // 分割数据
    private static function buildRepose(array $repose = []): array
    {
        $data = [
            'date' => [],
            'week' => [],
        ];
        if (empty($repose)) return $data;

        foreach ($repose as $item) {
            if ($item['type'] == StaffReposeModel::TYPE_DATE) {
                $data['date'][] = [
                    'id'            => $item['id'],
                    'reposes_type'  => $item['type'],
                    'reposes_value' => $item['param'],
                    'create_time'   => $item['create_time'],
                ];
            } else {
                $data['week'][] = [
                    'id'            => $item['id'],
                    'reposes_type'  => $item['type'],
                    'reposes_value' => $item['param'],
                    'create_time'   => $item['create_time'],
                ];
            }
        }
        return $data;
    }

    public function setRestTime(array $param = []): array
    {
        if ($param['type'] == 2) { // 周期设置,只有一条数据
            $del = StaffReposeModel::where('staff_id', $param['staff_id'])->where('type', 2)->find();
            if ($del) $del->delete();
        }
        $data             = [
            'staff_id'    => $param['staff_id'],
            'type'        => $param['type'],
            'param'       => $param['value'],
            'status'      => 1,
            'create_time' => time(),
        ];
        $staffReposeModel = new StaffReposeModel();
        $result           = $staffReposeModel->save($data);
        if (!$result) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN, '设置失败');
        }

        // 更新缓存
        (new StaffCacheService($param['staff_id']))->getData(true);
        // 这个方法只有前端api使用，所以直接用staff_id查询
        return $this->getStaff($param['staff_id']);
    }

    public function delRestTime(array $param = []): array
    {
        $id = intval($param['id']);
        if ($id <= 0) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
        }
        $repose = StaffReposeModel::where('id', $id)->find();
        if (!$repose) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
        }
        if (isset($param['staff_id']) && $param['staff_id'] > 0 && $repose->staff_id != $param['staff_id']) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
        }
        $result = $repose->delete();
        if (!$result) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN, '删除失败');
        }
        return success();
    }

    public function getArea($id = 0, StaffModel $staff = null): bool|array
    {
        if ($id > 0) {
            $staff = StaffModel::where('id', $id)->field(self::$basicColumns)->findOrEmpty();
        }
        if ($staff->isEmpty()) {
            return false;
        }
        return self::buildArea($staff->relRegion->toArray());
    }

    private static function buildArea(array $region = []): array
    {
        if (empty($region)) return [];
        $res           = [];
        $regionService = new RegionService();
        foreach ($region as $item) {
            array_push($res, [
                'id'          => $item['id'],
                'code'        => $item['region_code'],
                'name'        => $regionService->getRegionDetail($item['region_code'])['merger_name'] ?? '',
                'create_time' => $item['create_time'],
            ]);
        }
        return $res;
    }

    public function setArea(array $param = []): array
    {
        $allRegion = intval($param['all_region'] ?? 0);
        StaffModel::where('id', $param['staff_id'])->update(['all_region' => $allRegion]);
        if ($allRegion > 0) {
            // 不处理后续逻辑
            return $this->getStaff($param['staff_id']);
        }

        $del = StaffRegionModel::where('staff_id', $param['staff_id'])->find();
        if ($del) $del->delete();

        $codes = array_filter(array_unique(explode(',', $param['codes'])));
        if ($codes) {
            $data             = [
                'staff_id'    => $param['staff_id'],
                'region_code' => implode(',', $codes),
                'status'      => 1,
                'create_time' => time(),
            ];
            $staffRegionModel = new StaffRegionModel();
            $result           = $staffRegionModel->save($data);
            if (!$result) {
                return fail(Response::ACCOUNT_EDIT_FORBIDDEN, '设置失败');
            }
        }

        // 更新缓存
        (new StaffCacheService($param['staff_id']))->getData(true);
        // 这个方法只有前端api使用，所以直接用staff_id查询
        return $this->getStaff($param['staff_id']);
    }

    // 更新或保存身份证信息
    public function addIdCard(array $param = [], &$msg = ""): bool
    {
        // 开始事务
        Db::startTrans();
        try {
            $staffId = intval($param['staff_id']);
            if ($staffId <= 0) {
                $msg = "员工ID无效";
                return false;
            }

            // 验证员工是否存在
            $staff = StaffModel::where('id', $staffId)->find();
            if (!$staff) {
                $msg = "员工不存在";
                return false;
            }

            // 一次性查询所有身份证认证记录
            $existingRecordsQuery = StaffAuthnModel::where('staff_id', $staffId)
                ->whereIn('authn_type', [Authn::ID_CARD_FRONT, Authn::ID_CARD_BACK])
                ->select();

            // 手动建立索引
            $existingRecords = [];
            foreach ($existingRecordsQuery as $record) {
                $existingRecords[$record->authn_type] = $record;
            }

            $currentTime = time();
            $realName = $param['real_name'] ?? '';
            $idCardNumber = $param['id_card_number'] ?? '';

            // 准备批量操作的数据
            $updateData = [];
            $insertData = [];

            // 处理身份证正面和反面
            $cardTypes = [
                Authn::ID_CARD_FRONT => intval($param['id_card_front_image']),
                Authn::ID_CARD_BACK => intval($param['id_card_back_image'])
            ];

            foreach ($cardTypes as $authnType => $imageId) {
                $commonData = [
                    'image_id' => $imageId,
                    'authn_no' => $idCardNumber,
                    'name' => $realName,
                    'audit_status' => StaffAuthnModel::AUDIT_STATUS_UNAUDITED,
                    'audit_reason' => '',
                    'audit_time' => 0,
                    'audit_user_id' => 0,
                    'update_time' => $currentTime,
                ];

                if (isset($existingRecords[$authnType])) {
                    // 记录存在，准备更新
                    $updateData[] = array_merge($commonData, [
                        'id' => $existingRecords[$authnType]->id
                    ]);
                } else {
                    // 记录不存在，准备插入
                    $insertData[] = array_merge($commonData, [
                        'staff_id' => $staffId,
                        'authn_type' => $authnType,
                        'create_time' => $currentTime,
                    ]);
                }
            }

            // 批量更新现有记录
            if (!empty($updateData)) {
                foreach ($updateData as $data) {
                    $id = $data['id'];
                    unset($data['id']);
                    StaffAuthnModel::where('id', $id)->update($data);
                }
            }

            // 批量插入新记录
            if (!empty($insertData)) {
                StaffAuthnModel::insertAll($insertData);
            }

            // 更新员工表的真实姓名（如果提供了的话）
            if (!empty($realName)) {
                StaffModel::where('id', $staffId)->update([
                    'real_name' => $realName,
                    'update_time' => $currentTime,
                ]);
            }

            // 提交事务
            Db::commit();

            // 更新缓存
            (new StaffCacheService($staffId))->getData(true);

            $msg = "身份证信息提交成功";
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = "提交失败：" . $e->getMessage();
            return false;
        }
    }

    public function addTrain(array $param = []): array
    {
        $data     = [
            'staff_id'         => $param['staff_id'],
            'start_time'       => $param['start_date'],
            'end_time'         => $param['end_date'],
            'organization'     => $param['organization'],
            'topic'            => $param['topic'],
            'obtained'         => isset($param['obtained']) ? $param['obtained'] : '',
            'authn_id'         => isset($param['authn_id']) ? $param['authn_id'] : 0,
            'content_overview' => isset($param['content_overview']) ? $param['content_overview'] : '',
            'create_time'      => time(),
        ];
        $insertId = StaffTrainingModel::insertGetId($data);
        if ($insertId <= 0) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
        }
        // 更新缓存
        (new StaffCacheService($param['staff_id']))->getData(true);
        return $this->getStaff($param['staff_id']);
    }

    public function editTrain(array $param = []): array
    {
        // 开始事务
        Db::startTrans();
        try {
            $id = intval($param['id']);
            if ($id <= 0) {
                return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
            }
            $train = StaffTrainingModel::where('id', $id)->find();
            if (!$train) {
                return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
            }

            if (isset($param['start_time']) && $param['start_time'] != $train->start_time) {
                $train->start_time = $param['start_time'];
            }
            if (isset($param['end_time']) && $param['end_time'] != $train->end_time) {
                $train->end_time = $param['end_time'];
            }
            if (isset($param['organization']) && $param['organization'] != $train->organization) {
                $train->organization = $param['organization'];
            }
            if (isset($param['topic']) && $param['topic'] != $train->topic) {
                $train->topic = $param['topic'];
            }
            if (isset($param['obtained']) && $param['obtained'] != $train->obtained) {
                $train->obtained = $param['obtained'];
            }
            if (isset($param['authn_id']) && $param['authn_id'] != $train->authn_id) {
                $train->authn_id = $param['authn_id'];
            }
            if (isset($param['content_overview']) && $param['content_overview'] != $train->content_overview) {
                $train->content_overview = $param['content_overview'];
            }
            //  audit_status
            if (isset($param['audit_status'])) {
                $train->audit_status  = intval($param['audit_status']);
                $train->audit_reason  = $param['audit_reason'];
                $train->audit_time    = time();
                $train->audit_user_id = intval($param['admin_id']);
                // 添加审核流水
                AuditService::addAuditLog(intval($param['admin_id']), AuditModel::USER_ROLE_TYPE_SHOP, AuditModel::TYPE_STAFF_TRAINING, $id, intval($param['audit_status']), $param['audit_reason']);
            } else {
                // 用户修改，需要重新审核
                $train->audit_status  = 0;
                $train->audit_reason  = '';
                $train->audit_time    = 0;
                $train->audit_user_id = 0;
            }
            $train->update_time = time();
            $res                = $train->save();
            // 提交事务
            Db::commit();
            if (!$res) {
                return fail(Response::ROLE_SAVE_FAIL, '编辑失败');
            }
            // 更新缓存
            (new StaffCacheService($train['staff_id']))->getData(true);
            return $this->getStaff($train['staff_id']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return fail(Response::ROLE_SAVE_FAIL, '编辑失败');
        }
    }

    // 删除
    public function delTrain(array $param = []): array
    {
        $id = intval($param['id']);
        if ($id <= 0) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
        }
        $train = StaffTrainingModel::where('id', $id)->find();
        if (!$train) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
        }
        if (isset($param['staff_id']) && $param['staff_id'] > 0 && $train->staff_id != $param['staff_id']) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
        }
        $result = $train->delete();
        if (!$result) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN, '删除失败');
        }
        // 更新缓存
        (new StaffCacheService($param['staff_id']))->getData(true);
        return success();
    }

    // 更新或保存健康证信息
    public function addHealth(array $param = [], &$msg = ""): bool
    {
        // 开始事务
        Db::startTrans();
        try {
            $staffId = intval($param['staff_id']);
            if ($staffId <= 0) {
                $msg = "员工ID无效";
                return false;
            }

            // 验证员工是否存在
            $staff = StaffModel::where('id', $staffId)->find();
            if (!$staff) {
                $msg = "员工不存在";
                return false;
            }

            // 查询现有健康证记录
            $existingRecord = StaffAuthnModel::where('staff_id', $staffId)
                ->where('authn_type', Authn::HEALTH_CERTIFICATE)
                ->find();

            $currentTime = time();
            $healthCertificateNo = $param['health_certificate_no'] ?? '';
            $name = $staff->real_name;
            $issueDate = isset($param['issue_date']) ? strtotime($param['issue_date']) : 0;

            // 准备数据
            $data = [
                'image_id' => intval($param['health_certificate_image']),
                'authn_no' => $healthCertificateNo,
                'name' => $name,
                'issue_date' => $issueDate,
                'audit_status' => StaffAuthnModel::AUDIT_STATUS_UNAUDITED,
                'audit_reason' => '',
                'audit_time' => 0,
                'audit_user_id' => 0,
                'update_time' => $currentTime,
            ];

            if ($existingRecord) {
                // 更新现有记录
                StaffAuthnModel::where('id', $existingRecord->id)->update($data);
            } else {
                // 创建新记录
                $data = array_merge($data, [
                    'staff_id' => $staffId,
                    'authn_type' => Authn::HEALTH_CERTIFICATE,
                    'create_time' => $currentTime,
                ]);
                StaffAuthnModel::create($data);
            }

            // 提交事务
            Db::commit();

            // 更新缓存
            (new StaffCacheService($staffId))->getData(true);

            $msg = "健康证信息提交成功";
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = "提交失败：" . $e->getMessage();
            return false;
        }
    }

    // 新增技能证信息
    public function addSkill(array $param = [], &$msg = ""): bool
    {
        // 开始事务
        Db::startTrans();
        try {
            $staffId = intval($param['staff_id']);
            if ($staffId <= 0) {
                $msg = "员工ID无效";
                return false;
            }

            // 验证员工是否存在
            $staff = StaffModel::where('id', $staffId)->find();
            if (!$staff) {
                $msg = "员工不存在";
                return false;
            }

            $currentTime = time();
            $skillCertificateName = $param['skill_certificate_name'] ?? '';
            $skillCertificateNo = $param['skill_certificate_no'] ?? '';
            $issueDate = isset($param['issue_date']) && !empty($param['issue_date']) ? strtotime($param['issue_date']) : 0;
            $expiryDate = isset($param['expiry_date']) && !empty($param['expiry_date']) ? strtotime($param['expiry_date']) : 0;

            // 处理图片ID
            $imageIds = [];
            if (is_string($param['skill_certificate_images'])) {
                $imageIds = array_filter(explode(',', $param['skill_certificate_images']));
            } elseif (is_array($param['skill_certificate_images'])) {
                $imageIds = array_filter($param['skill_certificate_images']);
            }

            if (empty($imageIds)) {
                $msg = "请至少上传一张技能证图片";
                return false;
            }

            // 准备插入数据
            $insertData = [];
            foreach ($imageIds as $index => $imageId) {
                $data = [
                    'staff_id' => $staffId,
                    'authn_type' => Authn::SKILL_CERTIFICATE,
                    'image_id' => intval($imageId),
                    'name' => $skillCertificateName,
                    'authn_no' => $skillCertificateNo,
                    'issue_date' => $issueDate,
                    'expiry_date' => $expiryDate,
                    'audit_status' => StaffAuthnModel::AUDIT_STATUS_UNAUDITED,
                    'audit_reason' => '',
                    'audit_time' => 0,
                    'audit_user_id' => 0,
                    'create_time' => $currentTime,
                    'update_time' => $currentTime,
                ];

                // 主图片（第一张）
                if ($index === 0) {
                    $data['extra_images'] = count($imageIds) > 1 ? implode(',', array_slice($imageIds, 1)) : '';
                } else {
                    // 其他图片作为额外图片，不单独创建记录
                    continue;
                }

                $insertData[] = $data;
            }

            // 批量插入记录（实际只插入一条主记录）
            if (!empty($insertData)) {
                StaffAuthnModel::insertAll($insertData);
            }

            // 提交事务
            Db::commit();

            // 更新缓存
            (new StaffCacheService($staffId))->getData(true);

            $msg = "技能证信息添加成功";
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = "添加失败：" . $e->getMessage();
            return false;
        }
    }

    // 编辑技能证信息
    public function editSkill(array $param = [], &$msg = ""): bool
    {
        // 开始事务
        Db::startTrans();
        try {
            $staffId = intval($param['staff_id']);
            $skillId = intval($param['id'] ?? 0);

            if ($staffId <= 0) {
                $msg = "员工ID无效";
                return false;
            }

            if ($skillId <= 0) {
                $msg = "技能证ID无效";
                return false;
            }

            // 验证员工是否存在
            $staff = StaffModel::where('id', $staffId)->find();
            if (!$staff) {
                $msg = "员工不存在";
                return false;
            }

            // 查询现有技能证记录
            $existingRecord = StaffAuthnModel::where('id', $skillId)
                ->where('staff_id', $staffId)
                ->where('authn_type', Authn::SKILL_CERTIFICATE)
                ->find();

            if (!$existingRecord) {
                $msg = "技能证记录不存在";
                return false;
            }

            $currentTime = time();
            $skillCertificateName = $param['skill_certificate_name'] ?? '';
            $skillCertificateNo = $param['skill_certificate_no'] ?? '';
            $issueDate = isset($param['issue_date']) && !empty($param['issue_date']) ? strtotime($param['issue_date']) : 0;
            $expiryDate = isset($param['expiry_date']) && !empty($param['expiry_date']) ? strtotime($param['expiry_date']) : 0;

            // 处理图片ID
            $imageIds = [];
            if (is_string($param['skill_certificate_images'])) {
                $imageIds = array_filter(explode(',', $param['skill_certificate_images']));
            } elseif (is_array($param['skill_certificate_images'])) {
                $imageIds = array_filter($param['skill_certificate_images']);
            }

            if (empty($imageIds)) {
                $msg = "请至少上传一张技能证图片";
                return false;
            }

            // 准备更新数据
            $updateData = [
                'image_id' => intval($imageIds[0]), // 主图片
                'name' => $skillCertificateName,
                'authn_no' => $skillCertificateNo,
                'issue_date' => $issueDate,
                'expiry_date' => $expiryDate,
                'extra_images' => count($imageIds) > 1 ? implode(',', array_slice($imageIds, 1)) : '',
                'audit_status' => StaffAuthnModel::AUDIT_STATUS_UNAUDITED,
                'audit_reason' => '',
                'audit_time' => 0,
                'audit_user_id' => 0,
                'update_time' => $currentTime,
            ];

            // 更新记录
            StaffAuthnModel::where('id', $skillId)->update($updateData);

            // 提交事务
            Db::commit();

            // 更新缓存
            (new StaffCacheService($staffId))->getData(true);

            $msg = "技能证信息更新成功";
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = "更新失败：" . $e->getMessage();
            return false;
        }
    }

    // 删除技能证信息
    public function deleteSkill(array $param = [], &$msg = ""): bool
    {
        // 开始事务
        Db::startTrans();
        try {
            $staffId = intval($param['staff_id']);
            $skillId = intval($param['id'] ?? 0);

            if ($staffId <= 0) {
                $msg = "员工ID无效";
                return false;
            }

            if ($skillId <= 0) {
                $msg = "技能证ID无效";
                return false;
            }

            // 验证员工是否存在
            $staff = StaffModel::where('id', $staffId)->find();
            if (!$staff) {
                $msg = "员工不存在";
                return false;
            }

            // 查询现有技能证记录
            $existingRecord = StaffAuthnModel::where('id', $skillId)
                ->where('staff_id', $staffId)
                ->where('authn_type', Authn::SKILL_CERTIFICATE)
                ->find();

            if (!$existingRecord) {
                $msg = "技能证记录不存在";
                return false;
            }

            // 删除记录
            $existingRecord->delete();

            // 提交事务
            Db::commit();

            // 更新缓存
            (new StaffCacheService($staffId))->getData(true);

            $msg = "技能证删除成功";
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = "删除失败：" . $e->getMessage();
            return false;
        }
    }

    // 新增体检报告信息
    public function addPhysical(array $param = [], &$msg = ""): bool
    {
        // 开始事务
        Db::startTrans();
        try {
            $staffId = intval($param['staff_id']);
            if ($staffId <= 0) {
                $msg = "员工ID无效";
                return false;
            }

            // 验证员工是否存在
            $staff = StaffModel::where('id', $staffId)->find();
            if (!$staff) {
                $msg = "员工不存在";
                return false;
            }

            $currentTime = time();
            $reportNo = $param['report_no'] ?? '';
            $name = $param['name'] ?? '';
            $physicalDate = isset($param['physical_date']) ? strtotime($param['physical_date']) : 0;
            $reportType = intval($param['report_type']);

            // 准备基础数据
            $data = [
                'staff_id' => $staffId,
                'authn_type' => Authn::PHYSICAL_EXAMINATION_REPORT,
                'authn_no' => $reportNo,
                'name' => $name,
                'issue_date' => $physicalDate, // 体检时间存储在issue_date字段
                'report_type' => $reportType,
                'audit_status' => StaffAuthnModel::AUDIT_STATUS_UNAUDITED,
                'audit_reason' => '',
                'audit_time' => 0,
                'audit_user_id' => 0,
                'create_time' => $currentTime,
                'update_time' => $currentTime,
            ];

            // 根据上传方式处理不同的数据
            switch ($reportType) {
                case 1: // 链接方式
                    $data['report_url'] = $param['report_url'] ?? '';
                    $data['image_id'] = 0;
                    $data['pdf_id'] = 0;
                    $data['extra_images'] = '';
                    break;

                case 2: // PDF文件方式
                    $data['pdf_id'] = intval($param['pdf_file_id'] ?? 0);
                    $data['report_url'] = '';
                    $data['image_id'] = 0;
                    $data['extra_images'] = '';
                    break;

                case 3: // 多张图片方式
                    // 处理图片ID
                    $imageIds = [];
                    if (is_string($param['report_images'])) {
                        $imageIds = array_filter(explode(',', $param['report_images']));
                    } elseif (is_array($param['report_images'])) {
                        $imageIds = array_filter($param['report_images']);
                    }

                    if (empty($imageIds)) {
                        $msg = "请至少上传一张体检报告图片";
                        return false;
                    }

                    $data['image_id'] = intval($imageIds[0]); // 主图片
                    $data['extra_images'] = count($imageIds) > 1 ? implode(',', array_slice($imageIds, 1)) : '';
                    $data['report_url'] = '';
                    $data['pdf_id'] = 0;
                    break;

                default:
                    $msg = "无效的体检报告上传方式";
                    return false;
            }

            // 创建记录
            StaffAuthnModel::create($data);

            // 提交事务
            Db::commit();

            // 更新缓存
            (new StaffCacheService($staffId))->getData(true);

            $msg = "体检报告信息添加成功";
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = "添加失败：" . $e->getMessage();
            return false;
        }
    }

    // 编辑体检报告信息
    public function editPhysical(array $param = [], &$msg = ""): bool
    {
        // 开始事务
        Db::startTrans();
        try {
            $staffId = intval($param['staff_id']);
            $physicalId = intval($param['id'] ?? 0);

            if ($staffId <= 0) {
                $msg = "员工ID无效";
                return false;
            }

            if ($physicalId <= 0) {
                $msg = "体检报告ID无效";
                return false;
            }

            // 验证员工是否存在
            $staff = StaffModel::where('id', $staffId)->find();
            if (!$staff) {
                $msg = "员工不存在";
                return false;
            }

            // 查询现有体检报告记录
            $existingRecord = StaffAuthnModel::where('id', $physicalId)
                ->where('staff_id', $staffId)
                ->where('authn_type', Authn::PHYSICAL_EXAMINATION_REPORT)
                ->find();

            if (!$existingRecord) {
                $msg = "体检报告记录不存在";
                return false;
            }

            $currentTime = time();
            $reportNo = $param['report_no'] ?? '';
            $name = $param['name'] ?? '';
            $physicalDate = isset($param['physical_date']) ? strtotime($param['physical_date']) : 0;
            $reportType = intval($param['report_type']);

            // 准备更新数据
            $updateData = [
                'authn_no' => $reportNo,
                'name' => $name,
                'issue_date' => $physicalDate,
                'report_type' => $reportType,
                'audit_status' => StaffAuthnModel::AUDIT_STATUS_UNAUDITED,
                'audit_reason' => '',
                'audit_time' => 0,
                'audit_user_id' => 0,
                'update_time' => $currentTime,
            ];

            // 根据上传方式处理不同的数据
            switch ($reportType) {
                case 1: // 链接方式
                    $updateData['report_url'] = $param['report_url'] ?? '';
                    $updateData['image_id'] = 0;
                    $updateData['pdf_id'] = 0;
                    $updateData['extra_images'] = '';
                    break;

                case 2: // PDF文件方式
                    $updateData['pdf_id'] = intval($param['pdf_file_id'] ?? 0);
                    $updateData['report_url'] = '';
                    $updateData['image_id'] = 0;
                    $updateData['extra_images'] = '';
                    break;

                case 3: // 多张图片方式
                    // 处理图片ID
                    $imageIds = [];
                    if (is_string($param['report_images'])) {
                        $imageIds = array_filter(explode(',', $param['report_images']));
                    } elseif (is_array($param['report_images'])) {
                        $imageIds = array_filter($param['report_images']);
                    }

                    if (empty($imageIds)) {
                        $msg = "请至少上传一张体检报告图片";
                        return false;
                    }

                    $updateData['image_id'] = intval($imageIds[0]); // 主图片
                    $updateData['extra_images'] = count($imageIds) > 1 ? implode(',', array_slice($imageIds, 1)) : '';
                    $updateData['report_url'] = '';
                    $updateData['pdf_id'] = 0;
                    break;

                default:
                    $msg = "无效的体检报告上传方式";
                    return false;
            }

            // 更新记录
            StaffAuthnModel::where('id', $physicalId)->update($updateData);

            // 提交事务
            Db::commit();

            // 更新缓存
            (new StaffCacheService($staffId))->getData(true);

            $msg = "体检报告信息更新成功";
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = "更新失败：" . $e->getMessage();
            return false;
        }
    }

    // 删除体检报告信息
    public function deletePhysical(array $param = [], &$msg = ""): bool
    {
        // 开始事务
        Db::startTrans();
        try {
            $staffId = intval($param['staff_id']);
            $physicalId = intval($param['id'] ?? 0);

            if ($staffId <= 0) {
                $msg = "员工ID无效";
                return false;
            }

            if ($physicalId <= 0) {
                $msg = "体检报告ID无效";
                return false;
            }

            // 验证员工是否存在
            $staff = StaffModel::where('id', $staffId)->find();
            if (!$staff) {
                $msg = "员工不存在";
                return false;
            }

            // 查询现有体检报告记录
            $existingRecord = StaffAuthnModel::where('id', $physicalId)
                ->where('staff_id', $staffId)
                ->where('authn_type', Authn::PHYSICAL_EXAMINATION_REPORT)
                ->find();

            if (!$existingRecord) {
                $msg = "体检报告记录不存在";
                return false;
            }

            // 删除记录
            $existingRecord->delete();

            // 提交事务
            Db::commit();

            // 更新缓存
            (new StaffCacheService($staffId))->getData(true);

            $msg = "体检报告删除成功";
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = "删除失败：" . $e->getMessage();
            return false;
        }
    }

    // 新增疫苗接种单信息
    public function addVaccination(array $param = [], &$msg = ""): bool
    {
        // 开始事务
        Db::startTrans();
        try {
            $staffId = intval($param['staff_id']);
            if ($staffId <= 0) {
                $msg = "员工ID无效";
                return false;
            }

            // 验证员工是否存在
            $staff = StaffModel::where('id', $staffId)->find();
            if (!$staff) {
                $msg = "员工不存在";
                return false;
            }

            $currentTime = time();
            $vaccinationCertificateName = $param['vaccination_certificate_name'] ?? '';
            $vaccinationCertificateNo = $param['vaccination_certificate_no'] ?? '';
            $vaccinationDate = isset($param['vaccination_date']) && !empty($param['vaccination_date']) ? strtotime($param['vaccination_date']) : 0;
            $expiryDate = isset($param['expiry_date']) && !empty($param['expiry_date']) ? strtotime($param['expiry_date']) : 0;

            // 准备数据
            $data = [
                'staff_id' => $staffId,
                'authn_type' => Authn::VACCINATION_CERTIFICATE,
                'image_id' => intval($param['vaccination_certificate_image']),
                'name' => $vaccinationCertificateName,
                'authn_no' => $vaccinationCertificateNo,
                'issue_date' => $vaccinationDate,
                'expiry_date' => $expiryDate,
                'audit_status' => StaffAuthnModel::AUDIT_STATUS_UNAUDITED,
                'audit_reason' => '',
                'audit_time' => 0,
                'audit_user_id' => 0,
                'create_time' => $currentTime,
                'update_time' => $currentTime,
            ];

            // 创建记录
            StaffAuthnModel::create($data);

            // 提交事务
            Db::commit();

            // 更新缓存
            (new StaffCacheService($staffId))->getData(true);

            $msg = "疫苗接种单信息添加成功";
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = "添加失败：" . $e->getMessage();
            return false;
        }
    }

    // 编辑疫苗接种单信息
    public function editVaccination(array $param = [], &$msg = ""): bool
    {
        // 开始事务
        Db::startTrans();
        try {
            $staffId = intval($param['staff_id']);
            $vaccinationId = intval($param['id'] ?? 0);

            if ($staffId <= 0) {
                $msg = "员工ID无效";
                return false;
            }

            if ($vaccinationId <= 0) {
                $msg = "疫苗接种单ID无效";
                return false;
            }

            // 验证员工是否存在
            $staff = StaffModel::where('id', $staffId)->find();
            if (!$staff) {
                $msg = "员工不存在";
                return false;
            }

            // 查询现有疫苗接种单记录
            $existingRecord = StaffAuthnModel::where('id', $vaccinationId)
                ->where('staff_id', $staffId)
                ->where('authn_type', Authn::VACCINATION_CERTIFICATE)
                ->find();

            if (!$existingRecord) {
                $msg = "疫苗接种单记录不存在";
                return false;
            }

            $currentTime = time();
            $vaccinationCertificateName = $param['vaccination_certificate_name'] ?? '';
            $vaccinationCertificateNo = $param['vaccination_certificate_no'] ?? '';
            $vaccinationDate = isset($param['vaccination_date']) && !empty($param['vaccination_date']) ? strtotime($param['vaccination_date']) : 0;
            $expiryDate = isset($param['expiry_date']) && !empty($param['expiry_date']) ? strtotime($param['expiry_date']) : 0;

            // 准备更新数据
            $updateData = [
                'image_id' => intval($param['vaccination_certificate_image']),
                'name' => $vaccinationCertificateName,
                'authn_no' => $vaccinationCertificateNo,
                'issue_date' => $vaccinationDate,
                'expiry_date' => $expiryDate,
                'audit_status' => StaffAuthnModel::AUDIT_STATUS_UNAUDITED,
                'audit_reason' => '',
                'audit_time' => 0,
                'audit_user_id' => 0,
                'update_time' => $currentTime,
            ];

            // 更新记录
            StaffAuthnModel::where('id', $vaccinationId)->update($updateData);

            // 提交事务
            Db::commit();

            // 更新缓存
            (new StaffCacheService($staffId))->getData(true);

            $msg = "疫苗接种单信息更新成功";
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = "更新失败：" . $e->getMessage();
            return false;
        }
    }

    // 删除疫苗接种单信息
    public function deleteVaccination(array $param = [], &$msg = ""): bool
    {
        // 开始事务
        Db::startTrans();
        try {
            $staffId = intval($param['staff_id']);
            $vaccinationId = intval($param['id'] ?? 0);

            if ($staffId <= 0) {
                $msg = "员工ID无效";
                return false;
            }

            if ($vaccinationId <= 0) {
                $msg = "疫苗接种单ID无效";
                return false;
            }

            // 验证员工是否存在
            $staff = StaffModel::where('id', $staffId)->find();
            if (!$staff) {
                $msg = "员工不存在";
                return false;
            }

            // 查询现有疫苗接种单记录
            $existingRecord = StaffAuthnModel::where('id', $vaccinationId)
                ->where('staff_id', $staffId)
                ->where('authn_type', Authn::VACCINATION_CERTIFICATE)
                ->find();

            if (!$existingRecord) {
                $msg = "疫苗接种单记录不存在";
                return false;
            }

            // 删除记录
            $existingRecord->delete();

            // 提交事务
            Db::commit();

            // 更新缓存
            (new StaffCacheService($staffId))->getData(true);

            $msg = "疫苗接种单删除成功";
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = "删除失败：" . $e->getMessage();
            return false;
        }
    }

    // 保险
    public function addInsure(array $param = []): array
    {
        $staff = StaffModel::where('id', $param['staff_id'])->where('shop_id', $param['shop_id'])->find();
        if (!$staff) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN, '该员工不属于你的店铺');
        }
        $isExist = StaffInsureModel::where('staff_id', $param['staff_id'])
            ->where('start_time', '>= time', $param['start_time'])
            ->where('end_time', '<= time', $param['end_time'])
            ->find();
        if ($isExist) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN, '该时间段内已存在保险记录');
        }
        $data     = [
            'staff_id'    => $param['staff_id'],
            'image_id'    => $param['image_id'],
            'start_time'  => strtotime($param['start_time']),
            'end_time'    => strtotime($param['end_time']),
            'create_time' => time(),
        ];
        $insertId = StaffInsureModel::insertGetId($data);
        if ($insertId <= 0) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
        }
        // 更新缓存
        (new StaffCacheService($param['staff_id']))->getData(true);
        return $this->getStaff($param['staff_id']);
    }

    public function delInsure(array $param = []): array
    {
        $id = intval($param['id']);
        if ($id <= 0) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
        }
        $insure = StaffInsureModel::where('id', $id)->find();
        if (!$insure) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
        }
        $result = $insure->delete();
        if (!$result) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN, '删除失败');
        }
        return success();
    }

    // 设置服务项目
    public function setService(array $param = []): bool
    {
        if (!isset($param['service_codes'])) {
            return false;
        }
        $where = [
            'shop_id'  => intval($param['shop_id']),
            'staff_id' => intval($param['staff_id']),
        ];
        // 删除数据
        StaffServiceModel::where($where)->delete();

        $codes = $param['service_codes'] ?? '';
        $codes = array_filter(array_unique(explode(',', $codes)));
        if ($codes) {
            $data = [];
            foreach ($codes as $code) {
                $data[] = [
                    'shop_id'     => intval($param['shop_id']),
                    'staff_id'    => intval($param['staff_id']),
                    'service_id'  => $code,
                    'create_time' => time(),
                ];
            }
            StaffServiceModel::insertAll($data);
        }
        return true;
    }

    // 设置工种
    public function setWorker(array $param = []): bool
    {
        if (!isset($param['shop_id']) || !isset($param['staff_id'])) {
            return false;
        }
        // 获取服务人员服务
        $serviceIds = StaffServiceModel::where('shop_id', $param['shop_id'])->where('staff_id', $param['staff_id'])->field('service_id')->column('service_id');
        if (empty($serviceIds)) {
            return false;
        }
        // 查找basicId
        $basicId = GoodsCategoryModel::where('c_id', 'in', $serviceIds)->field('third_category_id')->field('third_category_id')->select()->column('third_category_id');
        // 获取服务人员服务工种
        $codes    = [];
        $clarkIds = GoodsCategoryBasicModel::where('c_id', 'in', $basicId)->field('clark_type_ids')->select()->column('clark_type_ids');
        if (!empty($clarkIds)) {
            $clarkId_str = "";
            foreach ($clarkIds as $k => $v) {
                $clarkId_str .= $v;
            }
            $clarkArray = explode(',', $clarkId_str);
            // 去空去重
            $codes = array_filter(array_unique($clarkArray));
        }
        if ($codes) {
            $existList = [];
            $exist     = StaffWorkerModel::where('shop_id', $param['shop_id'])->where('staff_id', $param['staff_id'])->field('id, worker_id,sort')->select()->toArray();
            if ($exist) {
                $existList = array_column($exist, null, 'worker_id');
            }
            $data = [];
            foreach ($codes as $code) {
                $sort = isset($existList[$code]) ? $existList[$code]['sort'] : 50;
                $item = [
                    'shop_id'     => intval($param['shop_id']),
                    'staff_id'    => intval($param['staff_id']),
                    'worker_id'   => $code,
                    'sort'        => $sort,
                    'create_time' => time(),
                ];
                array_push($data, $item);
            }
            if ($data) {
                // 删除数据
                StaffWorkerModel::where('shop_id', '=', intval($param['shop_id']))->where('staff_id', '=', intval($param['staff_id']))->delete();
                StaffWorkerModel::insertAll($data);
            }
        } else {
            // 删除数据
            StaffWorkerModel::where('shop_id', '=', intval($param['shop_id']))->where('staff_id', '=', intval($param['staff_id']))->delete();
        }
        return true;
    }

    // 设置排序
    public function setWorkerSort(array $param = []): bool
    {
        $id = intval($param['id'] ?? 0);
        if ($id <= 0) {
            return false;
        }
        $worker = StaffWorkerModel::where('id', $id)->field('id,sort')->find();
        if (!$worker) {
            return false;
        }
        $worker->sort = intval($param['sort'] ?? 50);
        $worker->save();
        // 更新缓存
        (new StaffCacheService($worker->staff_id))->getData(true);
        return true;
    }

    // 获取可用工作日
    public function getWorkList(array $param = []): array
    {
        $res   = [];
        $range = formatDateRange($param['start_date'], $param['end_date']);
        $items = StaffReposeCacheModel::where('staff_id', $param['staff_id'])->select()->toArray();
        if (empty($items)) {
            $res = array_keys($range);
        }
        $moveWeeks = [];
        foreach ($items as $item) {
            if ($item['type'] == StaffReposeModel::TYPE_DATE && isset($range[$item['param']])) {
                unset($range[$item['param']]);
            }
            if ($item['type'] == StaffReposeModel::TYPE_WEEK) {
                array_push($moveWeeks, $item['param']);
            }
        }
        if (!empty($moveWeeks)) {
            foreach ($range as $k => $r) {
                if (in_array($r, $moveWeeks)) {
                    unset($range[$k]);
                }
            }
        }

        $res      = array_keys($range);
        $workdays = OrderOccupancyModel::where('staff_id', $param['staff_id'])
            ->where('lock_date', '>=', $param['start_date'])
            ->where('lock_date', '<=', $param['end_date'])
            ->field('lock_date')
            ->column('lock_date');

        // 差集
        $diff = array_diff($res, $workdays);
        if (empty($diff)) {
            return [];
        }
        return $diff;
    }

    // 获取工作日历
    public function getWorkCalendar(array $param = []): array
    {
        $res   = [];
        $range = formatDateRange($param['start_date'], $param['end_date']);
        $items = StaffReposeCacheModel::where('staff_id', $param['staff_id'])
            ->field('type, param')
            ->select()
            ->toArray();
        $rDate = [];
        $rWeek = [];
        if (!empty($items)) {
            foreach ($items as $item) {
                if ($item['type'] == StaffReposeModel::TYPE_DATE) {
                    array_push($rDate, $item['param']);
                }
                if ($item['type'] == StaffReposeModel::TYPE_WEEK) {
                    array_push($rWeek, $item['param']);
                }
            }
        }
        // 获取锁
        $workdays = OrderOccupancyModel::where('staff_id', $param['staff_id'])
            ->where('lock_date', '>=', $param['start_date'])
            ->where('lock_date', '<=', $param['end_date'])
            ->field('order_id,lock_type,lock_date')
            ->select()
            ->toArray();
        $workday  = array_column($workdays, null, 'lock_date');
        foreach ($range as $d => $w) {
            $res[$d] = [
                'date'     => $d,
                'week'     => $w,
                'status'   => 'appointment',
                'order_id' => '',
            ];
            // 休息日
            if (in_array($w, $rWeek) || in_array($d, $rDate)) {
                $res[$d] = [
                    'date'     => $d,
                    'week'     => $w,
                    'status'   => 'restday',
                    'order_id' => '',
                ];
            }
            // 工作日覆盖休息日
            if (isset($workday[$d])) {
                $res[$d] = [
                    'date'     => $d,
                    'week'     => $w,
                    'status'   => $workday[$d]['lock_type'] == 'workday' ? 'workday' : 'lock',
                    'order_id' => $workday[$d]['order_id'],
                ];
            }
        }

        return $res;
    }

    // es缓存数据
    public function getListForEs(array $param = []): array
    {
        $field = [
            'id as staff_id',
            'shop_id',
            'real_name',
            'status as staff_status',
            'level_id',
            'update_time',
        ];

        $query = StaffModel::field($field);
        if (isset($param['last_time']) && !empty($param['last_time'])) {
            $query->where('update_time', '>=', strtotime($param['last_time']));
        }
        $rows = $query->paginate(intval($param['page_size'] ?? 10))->toArray();
        $sIds = array_column($rows['data'], 'staff_id');

        // 获取服务
        $service     = [];
        $serviceList = StaffServiceModel::where('staff_id', 'in', $sIds)->field('staff_id,service_id')->select()->toArray();
        if ($serviceList) {
            $categoryMap = [];
            $cids        = array_column($serviceList, 'service_id');
            // 去空去重
            $cids = array_filter(array_unique($cids));
            if (!empty($cids)) {
                $map                = GoodsCategoryBasicModel::getCategoryMap();
                $map                = array_column($map, 'name', 'c_id');
                $goodsCategoryQuery = GoodsCategoryModel::where('c_id', 'in', $cids)->field('c_id,first_category_id,second_category_id,third_category_id,third_category_unique_name')->select();
                if (!$goodsCategoryQuery->isEmpty()) {
                    foreach ($goodsCategoryQuery as $item) {
                        $categoryMap[$item->c_id] = [
                            'first_category_name'        => $map[$item->first_category_id] ?? '',
                            'second_category_name'       => $map[$item->second_category_id] ?? '',
                            'third_category_name'        => $map[$item->third_category_id] ?? '',
                            'third_category_unique_name' => $item->third_category_unique_name ?? '',
                        ];
                    }
                }
            }

            foreach ($serviceList as $item) {
                if (isset($categoryMap[$item['service_id']])) {
                    $cat                  = array_values($categoryMap[$item['service_id']]);
                    $cat                  = array_filter(array_unique($cat));
                    $item['service_name'] = implode(';', $cat);
                } else {
                    $item['service_name'] = "";
                }
                $service[$item['staff_id']][] = $item['service_name'];
            }
        }

        // 获取标签
        $tag     = [];
        $tagList = StaffTagModel::where('staff_id', 'in', $sIds)->field('staff_id,tag_id')->select()->toArray();
        if ($tagList) {
            $tagMap = [];
            $tids   = array_column($tagList, 'tag_id');
            // 去空去重
            $tids = array_filter(array_unique($tids));
            if (!empty($tids)) {
                $tagQuery = TagModel::where('id', 'in', $tids)->where('flag', 1)->where('show_status', 10)->field('id,name')->select();
                if (!$tagQuery->isEmpty()) {
                    $tagMap = array_column($tagQuery->toArray(), 'name', 'id');
                }
            }

            foreach ($tagList as $item) {
                $tag[$item['staff_id']][] = $tagMap[$item['tag_id']] ?? '';
            }
        }

        foreach ($rows['data'] as &$item) {
            if (isset($service[$item['staff_id']])) {
                $serviceName = implode(';', $service[$item['staff_id']]);
                $serviceName = array_filter(array_unique(explode(';', $serviceName)));
                $serviceName = implode(';', $serviceName);
            } else {
                $serviceName = '';
            }
            $item['service_name'] = $serviceName;

            if (isset($tag[$item['staff_id']])) {
                $tagName = array_filter(array_unique($tag[$item['staff_id']]));
                $tagName = implode(';', $tagName);
            } else {
                $tagName = '';
            }
            $item['tag_name']    = $tagName;
            $item['update_time'] = date('Y-m-d H:i:s', $item['update_time']);
        }
        return $rows;
    }

    // 获取佣金流水
    public function getCommissionList(array $param = []): array
    {
        $staffId = $param['staff_id'] ?? 0;
        if ($staffId <= 0) {
            return [];
        }

        $where = [];
        if (isset($param['staff_id']) && $param['staff_id']) {
            $where['staff_id'] = $param['staff_id'];
        }
        $list = CommissionDetailModel::where($where)
            ->field('id, sn, staff_id, fee, info, account_fee, type, create_time')
            ->order('id desc')
            ->paginate(config('admin.limit'));
        return $list->toArray();
    }
}
